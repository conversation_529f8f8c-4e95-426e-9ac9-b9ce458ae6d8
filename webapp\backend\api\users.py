from typing import Any, List
import logging
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from backend.core.security import get_current_active_user, get_password_hash
from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.cavo import Cavo
from backend.models.parco_cavi import ParcoCavo
from backend.models.strumento_certificato import StrumentoCertificato
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.schemas.user import UserCreate, UserInDB, UserUpdate

router = APIRouter()

@router.get("/", response_model=List[UserInDB])
def get_users(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista di tutti gli utenti.
    Solo gli amministratori possono accedere a questa funzionalità.

    Args:
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[UserInDB]: Lista degli utenti

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari
    """
    # Verifica che l'utente sia un amministratore o un utente impersonato da un amministratore
    token_data = getattr(current_user, "token_data", None)
    is_impersonated = token_data and getattr(token_data, "is_impersonated", False)

    if current_user.ruolo != "owner" and not is_impersonated:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    # Ottieni tutti gli utenti
    users = db.query(User).all()
    return users

@router.post("/", response_model=UserInDB)
def create_user(
    user_in: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea un nuovo utente standard.
    Solo gli amministratori possono creare nuovi utenti standard.
    Gli utenti cantiere vengono creati dagli utenti standard quando creano un cantiere.

    Args:
        user_in: Dati del nuovo utente
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        UserInDB: Utente creato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se l'username esiste già
    """
    # Verifica che l'utente sia un amministratore
    if current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    # Verifica che l'username non esista già
    db_user = db.query(User).filter(User.username == user_in.username).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username già in uso"
        )

    # Verifica che il ruolo sia valido
    # L'amministratore può creare solo utenti standard
    if user_in.ruolo != "user":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="L'amministratore può creare solo utenti standard"
        )

    # Crea il nuovo utente

    user = User(
        username=user_in.username,
        password=get_password_hash(user_in.password),
        password_plain=user_in.password,  # Salva la password in chiaro
        ruolo=user_in.ruolo,
        data_scadenza=user_in.data_scadenza,
        abilitato=user_in.abilitato,
        created_by=current_user.id_utente
    )

    db.add(user)
    db.commit()
    db.refresh(user)

    return user

@router.put("/{user_id}", response_model=UserInDB)
def update_user(
    user_id: int,
    user_in: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna un utente esistente.
    Solo gli amministratori possono aggiornare gli utenti.

    Args:
        user_id: ID dell'utente da aggiornare
        user_in: Dati aggiornati dell'utente
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        UserInDB: Utente aggiornato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se l'utente non esiste
    """
    # Verifica che l'utente sia un amministratore
    if current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    # Ottieni l'utente da aggiornare
    user = db.query(User).filter(User.id_utente == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utente non trovato"
        )

    # Aggiorna i campi dell'utente
    if user_in.username is not None:
        # Verifica che il nuovo username non sia già in uso
        if user_in.username != user.username:
            db_user = db.query(User).filter(User.username == user_in.username).first()
            if db_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username già in uso"
                )
        user.username = user_in.username

    if user_in.password is not None:
        user.password = get_password_hash(user_in.password)
        user.password_plain = user_in.password  # Aggiorna anche la password in chiaro

    if user_in.ruolo is not None:
        # Verifica che il ruolo sia valido
        if user_in.ruolo not in ["user", "cantieri_user", "owner"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Ruolo non valido"
            )
        # Non permettere di modificare il ruolo di admin
        if user.ruolo == "owner" and user_in.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Non è possibile modificare il ruolo dell'amministratore"
            )
        user.ruolo = user_in.ruolo

    if user_in.data_scadenza is not None:
        user.data_scadenza = user_in.data_scadenza

    if user_in.abilitato is not None:
        user.abilitato = user_in.abilitato

    db.commit()
    db.refresh(user)

    return user

@router.delete("/{user_id}", response_model=dict)
def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina definitivamente un utente e TUTTI i dati correlati dal database.
    ATTENZIONE: Questa è un'operazione irreversibile che:
    - Elimina tutte le bobine (parco_cavi) dei cantieri dell'utente
    - Elimina tutti gli strumenti certificati dei cantieri dell'utente
    - Elimina tutti i cavi dei cantieri dell'utente
    - Elimina tutti i cantieri dell'utente
    - Elimina l'utente stesso

    Solo gli amministratori possono eliminare gli utenti.

    Args:
        user_id: ID dell'utente da eliminare
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Messaggio di conferma

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se l'utente non esiste
    """
    # Verifica che l'utente sia un amministratore o un utente impersonato da un amministratore
    token_data = getattr(current_user, "token_data", None)
    is_impersonated = token_data and getattr(token_data, "is_impersonated", False)

    if current_user.ruolo != "owner" and not is_impersonated:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    # Ottieni l'utente da eliminare
    user = db.query(User).filter(User.id_utente == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utente non trovato"
        )

    # Non permettere di eliminare l'amministratore
    if user.ruolo == "owner":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Non è possibile eliminare l'amministratore"
        )

    try:

        # Ottieni prima l'elenco dei cantieri dell'utente per il logging
        cantieri = db.query(Cantiere).filter(Cantiere.id_utente == user_id).all()
        cantieri_ids = [cantiere.id_cantiere for cantiere in cantieri]

        # 1. Elimina tutte le bobine (parco_cavi) dei cantieri dell'utente
        db.query(ParcoCavo).filter(ParcoCavo.id_cantiere.in_(cantieri_ids)).delete(synchronize_session=False)

        # 2. Elimina tutti gli strumenti certificati dei cantieri dell'utente
        db.query(StrumentoCertificato).filter(StrumentoCertificato.id_cantiere.in_(cantieri_ids)).delete(synchronize_session=False)

        # 3. Elimina tutte le certificazioni dei cavi dei cantieri dell'utente
        db.query(CertificazioneCavo).filter(CertificazioneCavo.id_cantiere.in_(cantieri_ids)).delete(synchronize_session=False)

        # 4. Elimina tutti i cavi dei cantieri dell'utente
        db.query(Cavo).filter(Cavo.id_cantiere.in_(cantieri_ids)).delete(synchronize_session=False)

        # 5. Elimina tutti i cantieri dell'utente
        db.query(Cantiere).filter(Cantiere.id_utente == user_id).delete(synchronize_session=False)

        # 6. Elimina l'utente
        db.delete(user)

        # Commit delle modifiche
        db.commit()

        # Log dell'operazione
        logging.warning(f"🗑️ Utente ID {user_id} e tutti i suoi dati sono stati eliminati definitivamente")
        logging.warning(f"Cantieri eliminati: {cantieri_ids}")

        return {"message": "Utente e tutti i dati correlati eliminati con successo"}

    except Exception as e:
        db.rollback()
        logging.error(f"❌ Errore durante eliminazione utente: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'eliminazione dell'utente: {str(e)}"
        )

@router.get("/toggle/{user_id}", response_model=UserInDB)
def toggle_user_status(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Abilita o disabilita un utente.
    Solo gli amministratori possono modificare lo stato degli utenti.

    Args:
        user_id: ID dell'utente da modificare
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        UserInDB: Utente modificato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se l'utente non esiste
    """
    # Verifica che l'utente sia un amministratore o un utente impersonato da un amministratore
    token_data = getattr(current_user, "token_data", None)
    is_impersonated = token_data and getattr(token_data, "is_impersonated", False)

    if current_user.ruolo != "owner" and not is_impersonated:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    # Ottieni l'utente da modificare
    user = db.query(User).filter(User.id_utente == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utente non trovato"
        )

    # Non permettere di disabilitare l'amministratore
    if user.ruolo == "owner":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Non è possibile disabilitare l'amministratore"
        )

    # Inverte lo stato dell'utente
    user.abilitato = not user.abilitato
    db.commit()
    db.refresh(user)

    return user

@router.get("/db-raw", response_model=dict)
def get_db_raw(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene una visualizzazione raw del database.
    Solo gli amministratori possono accedere a questa funzionalità.

    Args:
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Dati del database con tutte le tabelle

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari
    """
    # Verifica che l'utente sia un amministratore o un utente impersonato da un amministratore
    token_data = getattr(current_user, "token_data", None)
    is_impersonated = token_data and getattr(token_data, "is_impersonated", False)

    if current_user.ruolo != "owner" and not is_impersonated:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    # Ottieni i dati di tutte le tabelle principali
    result = {}

    try:
        # 1. Tabella Utenti
        logging.info("Caricamento tabella Utenti")
        users = db.query(User).all()
        users_data = []
        for user in users:
            try:
                user_dict = {
                    "id_utente": user.id_utente,
                    "username": user.username,
                    "password": user.password_plain if user.password_plain else user.password,
                    "ruolo": user.ruolo,
                    "data_scadenza": user.data_scadenza.isoformat() if user.data_scadenza else None,
                    "abilitato": user.abilitato,
                    "created_by": user.created_by
                }
                users_data.append(user_dict)
            except Exception as e:
                logging.error(f"Errore durante l'elaborazione dell'utente {user.id_utente}: {str(e)}")
        result["users"] = users_data
        logging.info(f"Caricati {len(users_data)} utenti")

        # 2. Tabella Cantieri
        logging.info("Caricamento tabella Cantieri")
        cantieri = db.query(Cantiere).all()
        cantieri_data = []
        for cantiere in cantieri:
            try:
                cantiere_dict = {
                    "id_cantiere": cantiere.id_cantiere,
                    "nome": cantiere.nome,
                    "descrizione": cantiere.descrizione,
                    "data_creazione": cantiere.data_creazione.isoformat() if cantiere.data_creazione else None,
                    "password_cantiere": cantiere.password_cantiere,
                    "id_utente": cantiere.id_utente,
                    "codice_univoco": cantiere.codice_univoco
                }
                cantieri_data.append(cantiere_dict)
            except Exception as e:
                logging.error(f"Errore durante l'elaborazione del cantiere {cantiere.id_cantiere}: {str(e)}")
        result["cantieri"] = cantieri_data
        logging.info(f"Caricati {len(cantieri_data)} cantieri")

        # 3. Tabella Cavi
        logging.info("Caricamento tabella Cavi")
        cavi = db.query(Cavo).all()
        cavi_data = []
        for cavo in cavi:
            try:
                cavo_dict = {
                    "id_cavo": cavo.id_cavo,
                    "id_cantiere": cavo.id_cantiere,
                    "revisione_ufficiale": cavo.revisione_ufficiale,
                    "sistema": cavo.sistema,
                    "utility": cavo.utility,
                    "colore_cavo": cavo.colore_cavo,
                    "tipologia": cavo.tipologia,
                    "n_conduttori": cavo.n_conduttori,
                    "sezione": cavo.sezione,
                    "sh": cavo.sh,
                    "ubicazione_partenza": cavo.ubicazione_partenza,
                    "utenza_partenza": cavo.utenza_partenza,
                    "descrizione_utenza_partenza": cavo.descrizione_utenza_partenza,
                    "ubicazione_arrivo": cavo.ubicazione_arrivo,
                    "utenza_arrivo": cavo.utenza_arrivo,
                    "descrizione_utenza_arrivo": cavo.descrizione_utenza_arrivo,
                    "metri_teorici": cavo.metri_teorici,
                    "metratura_reale": cavo.metratura_reale,
                    "responsabile_posa": cavo.responsabile_posa,
                    "id_bobina": cavo.id_bobina,
                    "stato_installazione": cavo.stato_installazione,
                    "modificato_manualmente": cavo.modificato_manualmente,
                    "timestamp": cavo.timestamp.isoformat() if cavo.timestamp else None,
                    "collegamenti": cavo.collegamenti,
                    "responsabile_partenza": cavo.responsabile_partenza,
                    "responsabile_arrivo": cavo.responsabile_arrivo,
                    "comanda_posa": cavo.comanda_posa,
                    "comanda_partenza": cavo.comanda_partenza,
                    "comanda_arrivo": cavo.comanda_arrivo
                }
                cavi_data.append(cavo_dict)
            except Exception as e:
                logging.error(f"Errore durante l'elaborazione del cavo {cavo.id_cavo}: {str(e)}")
        result["cavi"] = cavi_data
        logging.info(f"Caricati {len(cavi_data)} cavi")

        # 4. Tabella Parco Cavi (Bobine)
        logging.info("Caricamento tabella Parco Cavi")
        bobine = db.query(ParcoCavo).all()
        bobine_data = []
        for bobina in bobine:
            try:
                bobina_dict = {
                    "id_bobina": bobina.id_bobina,
                    "numero_bobina": bobina.numero_bobina,
                    "utility": bobina.utility,
                    "tipologia": bobina.tipologia,
                    "n_conduttori": bobina.n_conduttori,
                    "sezione": bobina.sezione,
                    "metri_totali": bobina.metri_totali,
                    "metri_residui": bobina.metri_residui,
                    "stato_bobina": bobina.stato_bobina,
                    "ubicazione_bobina": bobina.ubicazione_bobina,
                    "fornitore": bobina.fornitore,
                    "n_DDT": bobina.n_DDT,
                    "data_DDT": bobina.data_DDT,
                    "configurazione": bobina.configurazione,
                    "id_cantiere": bobina.id_cantiere
                }
                bobine_data.append(bobina_dict)
            except Exception as e:
                logging.error(f"Errore durante l'elaborazione della bobina {bobina.id_bobina}: {str(e)}")
        result["parco_cavi"] = bobine_data
        logging.info(f"Caricati {len(bobine_data)} bobine")

        # 5. Tabella Strumenti Certificati
        logging.info("Caricamento tabella Strumenti Certificati")
        strumenti = db.query(StrumentoCertificato).all()
        strumenti_data = []
        for strumento in strumenti:
            try:
                strumento_dict = {
                    "id_strumento": strumento.id_strumento,
                    "id_cantiere": strumento.id_cantiere,
                    "nome_strumento": strumento.nome_strumento,
                    "marca": strumento.marca,
                    "modello": strumento.modello,
                    "numero_serie": strumento.numero_serie,
                    "data_taratura": strumento.data_taratura.isoformat() if strumento.data_taratura else None,
                    "data_scadenza": strumento.data_scadenza.isoformat() if strumento.data_scadenza else None,
                    "certificato_taratura": strumento.certificato_taratura,
                    "note": strumento.note,
                    "timestamp_creazione": strumento.timestamp_creazione.isoformat() if strumento.timestamp_creazione else None
                }
                strumenti_data.append(strumento_dict)
            except Exception as e:
                logging.error(f"Errore durante l'elaborazione dello strumento {strumento.id_strumento}: {str(e)}")
        result["strumenti_certificati"] = strumenti_data
        logging.info(f"Caricati {len(strumenti_data)} strumenti certificati")

        # 6. Tabella Certificazioni Cavi
        logging.info("Caricamento tabella Certificazioni Cavi")
        certificazioni = db.query(CertificazioneCavo).all()
        certificazioni_data = []
        for certificazione in certificazioni:
            try:
                certificazione_dict = {
                    "id_certificazione": certificazione.id_certificazione,
                    "id_cantiere": certificazione.id_cantiere,
                    "id_cavo": certificazione.id_cavo,
                    "numero_certificato": certificazione.numero_certificato,
                    "data_certificazione": certificazione.data_certificazione.isoformat() if certificazione.data_certificazione else None,
                    "id_operatore": certificazione.id_operatore,
                    "strumento_utilizzato": certificazione.strumento_utilizzato,
                    "id_strumento": certificazione.id_strumento,
                    "lunghezza_misurata": certificazione.lunghezza_misurata,
                    "valore_continuita": certificazione.valore_continuita,
                    "valore_isolamento": certificazione.valore_isolamento,
                    "valore_resistenza": certificazione.valore_resistenza,
                    "percorso_certificato": certificazione.percorso_certificato,
                    "percorso_foto": certificazione.percorso_foto,
                    "note": certificazione.note,
                    "timestamp_creazione": certificazione.timestamp_creazione.isoformat() if certificazione.timestamp_creazione else None,
                    "timestamp_modifica": certificazione.timestamp_modifica.isoformat() if certificazione.timestamp_modifica else None
                }
                certificazioni_data.append(certificazione_dict)
            except Exception as e:
                logging.error(f"Errore durante l'elaborazione della certificazione {certificazione.id_certificazione}: {str(e)}")
        result["certificazioni_cavi"] = certificazioni_data
        logging.info(f"Caricati {len(certificazioni_data)} certificazioni cavi")

    except Exception as e:
        logging.error(f"Errore durante il caricamento dei dati del database: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il caricamento dei dati del database: {str(e)}"
        )

    logging.info("Caricamento dati database completato con successo")
    return result

@router.post("/check-expired", response_model=dict)
def check_expired_users(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Verifica e disabilita automaticamente gli utenti la cui data di scadenza è passata.
    Solo gli amministratori possono eseguire questa operazione manualmente.
    Questa funzione viene chiamata anche automaticamente dal sistema.

    Args:
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Messaggio di conferma con il numero di utenti disabilitati

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari
    """
    # Verifica che l'utente sia un amministratore o un utente impersonato da un amministratore se la chiamata è manuale
    if current_user:
        token_data = getattr(current_user, "token_data", None)
        is_impersonated = token_data and getattr(token_data, "is_impersonated", False)

        if current_user.ruolo != "owner" and not is_impersonated:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questa risorsa"
            )

    # Ottieni la data corrente
    today = datetime.date.today()

    # Trova tutti gli utenti standard abilitati con data di scadenza passata
    expired_users = db.query(User).filter(
        User.ruolo == "user",
        User.abilitato == True,
        User.data_scadenza.isnot(None),
        User.data_scadenza < today
    ).all()

    # Disabilita gli utenti scaduti
    count = 0
    for user in expired_users:
        user.abilitato = False
        count += 1
        logging.warning(f"🔒 Utente {user.username} (ID: {user.id_utente}) disabilitato automaticamente per scadenza")

    # Commit delle modifiche
    if count > 0:
        db.commit()

    return {
        "message": f"{count} utenti disabilitati per scadenza",
        "disabled_count": count
    }
